import { Vec2 } from "planck";
import { PhysicalBody } from "./body.js";
import Renderer from "./renderer.js";

export default class Universe {

  constants = Object.freeze({
    G: 6.6 / 100
  });

  #animationFrameId;
  #timeoutId;
  /** @type {PhysicalBody[]} */
  #bodies = [];

  /** @param {Renderer} renderer */
  constructor(renderer) {
    this.renderer = renderer;
  }

  /** @param {PhysicalBody} body */
  addBody(body) {
    this.#bodies.push(body);
    body.renderer = this.renderer;
  }

  /** @param {ConstructorParameters<typeof PhysicalBody>} args */
  emplaceBody(...args) {
    const body = new PhysicalBody(...args);
    this.addBody(body);
    return body;
  }

  start() {
    this.#loop();
  }
  pause() {
    cancelAnimationFrame(this.#animationFrameId);
  }

  update(delta) {
    const bodyCount = this.#bodies.length;
    for(let i = 0; i < bodyCount; i++) {
      const bodyA = this.#bodies[i];
      bodyA.acceleration.setZero();
      for(let j = 0; j < bodyCount; j++) {
        if(i === j) {
          continue;
        }
        const bodyB = this.#bodies[j];
        const distanceSquare = Math.max(1, Math.min(1000, Vec2.distanceSquared(bodyA.position, bodyB.position)));
        const directionA = Vec2.sub(bodyB.position, bodyA.position);
        let gravitationForce = distanceSquare === 0 ? 0 : this.constants.G * bodyB.mass / distanceSquare;
        if(distanceSquare < (bodyA.mass / 4 + bodyB.mass / 4) ** 2) {
          gravitationForce *= -1;
          // bodyA.velocity.neg();
        }
        bodyA.acceleration.add(Vec2.mulVec2Num(directionA, gravitationForce / bodyA.mass));
        // bodyB.acceleration.set(Vec2.mulVec2Num(directionA, -gravitationForce));
      }
      bodyA.acceleration.mul(1 / (bodyCount - 1));
    }
    for(const body of this.#bodies) {
      body.update(delta);
    }
  }

  render() {
    this.renderer.clear();
    this.renderer.fill("#111");
    for(const body of this.#bodies) {
      body.render();
    }
  }

  #loop() {
    // this.#timeoutId = setTimeout()
    this.#animationFrameId = requestAnimationFrame(delta => {
      this.update(delta / 1000);
      this.render();
      this.#loop();
    });
  }

};