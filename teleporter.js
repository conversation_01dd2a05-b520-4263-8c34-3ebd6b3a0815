import { Vec2 } from "planck";
import Color from "./color.js";
import Particle from "./particle.js";

export class TeleporterComponent {
  /** @type {[ row: number, column: number ]} */
  gridMapPosition = [];
  /** @type {Teleporter} */
  teleporter;

  /**
   * @param {number} direction
   * @param {import("planck").Vec2Value} position
   * @param {number} radius
   * @param {Color} color
   */
  constructor(direction, position, radius, color) {
    this.position = new Vec2(position);
    this.color = color;
    this.radius = radius;
    this.forceFactor = direction;
  }
  update(delta) {}
};

export default class Teleporter {

  /** @type {ParticleSystem} */
  particleSystem;

  /**
   * @param {TeleporterComponent} inComponent
   * @param {TeleporterComponent} outComponent
   */
  constructor(inComponent, outComponent) {
    this.inComponent = inComponent;
    this.outComponent = outComponent;
    this.inComponent.teleporter = this;
    this.outComponent.teleporter = this;
  }

  /**
   * @param {TeleporterComponent} component
   * @param {Particle} particle
   * @param {number} distance
   * @param {Vec2} direction
   */
  handleParticle(component, particle, distance, direction) {
    if(component === this.inComponent && distance < 10 / meterPixelRatio) {
      particle.position.set(Vec2.mulVec2Num(direction, distance).add(this.outComponent.position));
      return 0;
    }
    if(distance <= component.radius) {
      particle.position.x += Math.sin((particle.position.y + component.position.y) * 10) / 100
      particle.position.y += Math.cos((particle.position.x + component.position.x) * 10) / 100
      return component.forceFactor;
    }
    return 0;
  }

  update(delta) {
    this.inComponent.update(delta);
    this.outComponent.update(delta);
  }

};