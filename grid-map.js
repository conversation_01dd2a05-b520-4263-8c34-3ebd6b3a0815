/** @template T */
export default class GridMap {

  /** @type {Set<T>[][]} */
  #data;
  #rows;
  #columns;

  /**
   * @param {number} rows
   * @param {number} columns
   */
  constructor(rows, columns) {
    this.#rows = rows;
    this.#columns = columns;
    this.#init();
  }

  get rows() {
    return this.#rows;
  }

  get columns() {
    return this.#columns;
  }

  #init() {
    this.#data = new Array(this.#rows);
    for(let row = 0; row < this.#rows; row++) {
      this.#data[row] = new Array(this.#columns);
      for(let column = 0; column < this.#columns; column++) {
        this.#data[row][column] = new Set;
      }
    }
  }

  /**
   * @param {number} row
   * @param {number} column
   */
  isValid(row, column) {
    if(row < 0 || row >= this.#rows) {
      return false;
    }
    if(column < 0 || column >= this.#columns) {
      return false;
    }
    return true;
  }

  /**
   * @param {number} row
   * @param {number} column
   */
  get(row, column) {
    return this.#data[row]?.[column]?.values() ?? [];
  }

  /**
   * @param {number} row
   * @param {number} column
   * @param {T} value
   */
  add(row, column, value) {
    if(!this.isValid(row, column)) {
      return false;
    }
    this.#data[row][column].add(value);
    return true;
  }

  /**
   * @param {number} row
   * @param {number} column
   * @param {T} value
   */
  delete(row, column, value) {
    if(!this.isValid(row, column)) {
      return false;
    }
    this.#data[row][column].delete(value);
    return true;
  }


  /**
   * @param {number} row
   * @param {number} column
   * @param {T} value
   */
  move(oldRow, oldColumn, row, column, value) {
    this.#data[oldRow]?.[oldColumn]?.delete(value);
    if(!this.isValid(row, column)) {
      return false;
    }
    this.#data[row][column].add(value);
    return true;
  }

  /**
   * @param {number} startRow
   * @param {number} startColumn
   * @param {number} endRow
   * @param {number} endColumn
   */
  *valuesBetween(startRow, startColumn, endRow, endColumn) {
    /** @type {[ row: number, column: number, value: T, wrapDirection: { x: 1 | -1 | 0; y: 1 | -1 | 0 } ]} */
    const entry = [];
    // startRow = Math.max(0, startRow);
    // startColumn = Math.max(0, startColumn);
    // endRow = Math.min(endRow, this.#rows);
    // endColumn = Math.min(endColumn, this.#columns);
    for(let row = startRow; row < endRow; row++) {
      entry[0] = (row % this.#rows + this.#rows) % this.#rows;
      for(let column = startColumn; column < endColumn; column++) {
        entry[1] = (column % this.#columns + this.#columns) % this.#columns;
        for(const value of this.#data[entry[0]][entry[1]]) {
          entry[2] = value;
          entry[3] = {
            x: row < 0 ? -1 : row >= this.#rows ? 1 : 0,
            y: column < 0 ? -1 : column >= this.#columns ? 1 : 0
          };
          yield entry;
        }
      }
    }
  }

  /**
   * @param {number} row
   * @param {number} column
   * @param {number} radius
   */
  valuesInRange(row, column, radius) {
    const startRow = Math.floor(row - radius);
    const startColumn = Math.floor(column - radius);
    const endRow = Math.floor(row + radius);
    const endColumn = Math.floor(column + radius);
    return this.valuesBetween(startRow, startColumn, endRow, endColumn);
  }

  *cell(start = 0, count = 1) {
    const end = start + count;
    for(let index = start; index < end; index++) {
      const row = index % this.#rows;
      const column = Math.floor(index / this.#rows) % this.#columns;
      for(const item of this.#data[row][column]) {
        yield item;
      }
    }
  }

};