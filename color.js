export default class Color {

  /** @type {Map<string, Readonly<Color>>} */
  static #refs = new Map;

  /**
   * @param {number} r 0 - 1
   * @param {number} g 0 - 1
   * @param {number} b 0 - 1
   * @param {number} [a] 0 - 1
  */
  constructor(r, g, b, a = 1) {
    this.r = r;
    this.g = g;
    this.b = b;
    this.a = a;
  }

  copy() {
    return new Color(this.r, this.g, this.b, this.a);
  }

  toString() {
    return Color.#id(this.r, this.g, this.b, this.a);
  }

  *[Symbol.iterator]() {
    yield this.r;
    yield this.g;
    yield this.b;
    yield this.a;
  }

  /** @param {`${ number }-${ number }-${ number }-${ number }`} text */
  static fromString(text) {
    return new Color(...text.split("-"));
  }

  /** @param {{ r: number; g: number; b: number; a?: number }} object */
  static from(object, mutable = true) {
    if(mutable) {
      return new Color(object.r, object.g, object.b, object.a);
    }
    return Color.const(object.r, object.g, object.b, object.a);
  }

  static #id(r, g, b, a = 1) {
    return `${ r }-${ g }-${ b }-${ a }`;
  }

  static const(r, g, b, a = 1) {
    const id = Color.#id(r, g, b, a);
    let instance = this.#refs.get(id);
    if(instance === undefined) {
      instance = Object.freeze(new Color(r, g, b, a));
      this.#refs.set(id, instance);
    }
    return instance;
  }

};