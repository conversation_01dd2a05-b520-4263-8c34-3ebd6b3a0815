<!doctype html>

<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>WebGL 2023 - 01 Hello, Triangle!</title>

    <style>
      html, head, body {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        background-color: #2a2a2a;
      }

      body {
        overflow: hidden;
      }

      #demo-canvas {

        /*
         * WebGL trick: pick an offensive canvas background color to test
         *  opacity/clearing bugs
         */
        background-color: #000000;

        /* Help to illustrate the rasterizer's job: when artificially lowering resolution, show pixelated image */
        image-rendering: crisp-edges;
        /* filter: blur(1.5px) contrast(3); */
      }

      /** CSS to make a nice little error box output for debugging (nice if you can't use devtools, e.g. mobile)  */
      #error-box {
        color: #fd8080;
        font-weight: 500;
        font-size: 18pt;
        border: 1px solid white;
      }

      .error-box-title {
        color: #eee;
        border-bottom: 1px solid gray;
      }
      output.g p#f :where(a, p) {
        position: fixed;
        font-family: 'Courier New', Courier, monospace;
        font-size: large;
        color: #fff;
      }
    </style>
    <script type="importmap">
      {
        "imports": {
          "planck": "./node_modules/planck/dist/planck.mjs"
        }
      }
    </script>
  </head>
  <body>
    <canvas id="demo-canvas" width="100" height="100">
      <!-- This message shows up only if the Canvas element isn't supported -->
      HTML5 canvas not supported in your browser! These demos will not work.
    </canvas>

    <output id="output">0</output>
    <script type="module" src="hello-triangle.js"></script>
  </body>
</html>