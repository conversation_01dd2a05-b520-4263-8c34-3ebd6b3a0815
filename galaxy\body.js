import { Vec2 } from "planck";
import Renderer from "./renderer.js";

export class PhysicalBody {
  /** @type {Renderer} */
  renderer = null;
  /**
   * @param {Vec2} position
   * @param {number} mass
   * @param {Color} color
   */
  constructor(position, mass, color) {
    this.position = position;
    this.velocity = randomVec2(-2, 2);
    this.acceleration = Vec2.zero();
    this.mass = mass;
    this.color = color;
    this.isStatic = false;
  }
  update(delta) {
    if(this.isStatic) {
      return;
    }
    // delta = Math.min(delta, 1);
    this.velocity.add(this.acceleration.mul(delta));
    const length = this.velocity.length();
    this.velocity.mul(delta);
    if(length > 0.05) {
      this.velocity.mul(0.05 / length);
    }
    this.position.add(this.velocity);
  }
  /** @param {Renderer} renderer */
  render() {
    this.renderer.circle(this.position, this.mass / 4, "#eee");
    this.renderer.circleLines(this.position, this.mass / 4, 2, this.color);
    // this.renderer.circleLines(this.position, (this.mass + 1) / 4, 1, "#eee");
  }
};

export function randomVec2(min, max) {
  return new Vec2(
    (Math.floor(Math.random() * (max - min + 1)) + min),
    (Math.floor(Math.random() * (max - min + 1)) + min)
  );
}