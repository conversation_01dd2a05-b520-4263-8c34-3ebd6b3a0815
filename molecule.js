import { Vec2 } from "planck";
import Color from "./color.js";

// Define real atomic properties with chemistry textbook colors
export const AtomicData = {
  H: {
    name: "Hydrogen",
    color: new Color(0.9, 0.9, 0.9, 1.0), // Light gray
    radius: 1.0,
    mass: 1,
    bonds: 1,
    electronegativity: 2.1
  },
  C: {
    name: "Carbon",
    color: new Color(0.1, 0.1, 0.1, 1.0), // Dark gray/black
    radius: 1.2,
    mass: 12,
    bonds: 4,
    electronegativity: 2.5
  },
  N: {
    name: "Nitrogen",
    color: new Color(0.2, 0.4, 0.9, 1.0), // Blue
    radius: 1.1,
    mass: 14,
    bonds: 3,
    electronegativity: 3.0
  },
  O: {
    name: "Oxygen",
    color: new Color(0.9, 0.1, 0.1, 1.0), // Red
    radius: 1.0,
    mass: 16,
    bonds: 2,
    electronegativity: 3.5
  },
  F: {
    name: "Fluorine",
    color: new Color(0.2, 0.8, 0.2, 1.0), // Green
    radius: 0.9,
    mass: 19,
    bonds: 1,
    electronegativity: 4.0
  },
  Cl: {
    name: "Chlorine",
    color: new Color(0.1, 0.7, 0.1, 1.0), // Dark green
    radius: 1.4,
    mass: 35,
    bonds: 1,
    electronegativity: 3.0
  }
};

export class Atom {
  constructor(element, position) {
    this.element = element;
    this.data = AtomicData[element];
    this.position = Vec2.clone(position);
    this.velocity = Vec2.zero();
    this.bonds = [];
    this.maxBonds = this.data.bonds;
    this.id = Math.random().toString(36).substr(2, 9);
  }

  get color() {
    return this.data.color;
  }

  get radius() {
    return this.data.radius;
  }

  get mass() {
    return this.data.mass;
  }

  canBond() {
    return this.bonds.length < this.maxBonds;
  }

  bondTo(otherAtom, bondType = 'single') {
    if (!this.canBond() || !otherAtom.canBond()) return false;

    const bond = new Bond(this, otherAtom, bondType);
    this.bonds.push(bond);
    otherAtom.bonds.push(bond);
    return bond;
  }

  update(deltaTime) {
    // Apply velocity damping
    this.velocity.mul(0.95);

    // Update position
    this.position.x += this.velocity.x * deltaTime;
    this.position.y += this.velocity.y * deltaTime;
  }
}

export class Bond {
  constructor(atom1, atom2, type = 'single') {
    this.atom1 = atom1;
    this.atom2 = atom2;
    this.type = type;
    this.length = this.getIdealLength();
    this.strength = this.getBondStrength();
  }

  getIdealLength() {
    // Ideal bond length based on atomic radii
    return (this.atom1.radius + this.atom2.radius) * 0.8;
  }

  getBondStrength() {
    const typeStrengths = { single: 1.0, double: 2.0, triple: 3.0 };
    return typeStrengths[this.type] || 1.0;
  }

  getCurrentLength() {
    return Vec2.distance(this.atom1.position, this.atom2.position);
  }

  applyForces() {
    const currentLength = this.getCurrentLength();
    const displacement = currentLength - this.length;

    if (Math.abs(displacement) < 0.01) return;

    const direction = Vec2.sub(this.atom2.position, this.atom1.position);
    direction.normalize();

    const force = displacement * this.strength * 0.1;

    // Apply spring force
    this.atom1.velocity.add(Vec2.mul(direction, force / this.atom1.mass));
    this.atom2.velocity.add(Vec2.mul(direction, -force / this.atom2.mass));
  }
}

// Predefined molecular templates
export const MoleculeTemplates = {
  water: () => {
    const O = new Atom('O', Vec2.zero());
    const H1 = new Atom('H', new Vec2(1.5, 0.8));
    const H2 = new Atom('H', new Vec2(1.5, -0.8));

    O.bondTo(H1);
    O.bondTo(H2);

    return [O, H1, H2];
  },

  methane: () => {
    const C = new Atom('C', Vec2.zero());
    const H1 = new Atom('H', new Vec2(2.0, 0));
    const H2 = new Atom('H', new Vec2(-2.0, 0));
    const H3 = new Atom('H', new Vec2(0, 2.0));
    const H4 = new Atom('H', new Vec2(0, -2.0));

    C.bondTo(H1);
    C.bondTo(H2);
    C.bondTo(H3);
    C.bondTo(H4);

    return [C, H1, H2, H3, H4];
  },

  ammonia: () => {
    const N = new Atom('N', Vec2.zero());
    const H1 = new Atom('H', new Vec2(1.5, 0.5));
    const H2 = new Atom('H', new Vec2(-1.5, 0.5));
    const H3 = new Atom('H', new Vec2(0, -1.8));

    N.bondTo(H1);
    N.bondTo(H2);
    N.bondTo(H3);

    return [N, H1, H2, H3];
  },

  carbonDioxide: () => {
    const C = new Atom('C', Vec2.zero());
    const O1 = new Atom('O', new Vec2(-2.5, 0));
    const O2 = new Atom('O', new Vec2(2.5, 0));

    C.bondTo(O1, 'double');
    C.bondTo(O2, 'double');

    return [C, O1, O2];
  },

  ethane: () => {
    const C1 = new Atom('C', new Vec2(-1.5, 0));
    const C2 = new Atom('C', new Vec2(1.5, 0));
    const H1 = new Atom('H', new Vec2(-3.0, 1.0));
    const H2 = new Atom('H', new Vec2(-3.0, -1.0));
    const H3 = new Atom('H', new Vec2(-1.5, 2.0));
    const H4 = new Atom('H', new Vec2(3.0, 1.0));
    const H5 = new Atom('H', new Vec2(3.0, -1.0));
    const H6 = new Atom('H', new Vec2(1.5, 2.0));

    C1.bondTo(C2);
    C1.bondTo(H1);
    C1.bondTo(H2);
    C1.bondTo(H3);
    C2.bondTo(H4);
    C2.bondTo(H5);
    C2.bondTo(H6);

    return [C1, C2, H1, H2, H3, H4, H5, H6];
  }
};

export class Molecule {
  constructor(atoms) {
    this.atoms = atoms;
    this.bonds = this.getAllBonds();
    this.center = this.calculateCenter();
    this.velocity = Vec2.zero();
  }

  getAllBonds() {
    const bonds = new Set();
    for (const atom of this.atoms) {
      for (const bond of atom.bonds) {
        bonds.add(bond);
      }
    }
    return Array.from(bonds);
  }

  calculateCenter() {
    const center = Vec2.zero();
    for (const atom of this.atoms) {
      center.add(atom.position);
    }
    center.mul(1 / this.atoms.length);
    return center;
  }

  update(deltaTime) {
    // Apply bond forces
    for (const bond of this.bonds) {
      bond.applyForces();
    }

    // Update atoms
    for (const atom of this.atoms) {
      atom.update(deltaTime);
    }

    // Update center
    this.center = this.calculateCenter();
  }

  moveTo(position) {
    const offset = Vec2.sub(position, this.center);
    for (const atom of this.atoms) {
      atom.position.add(offset);
    }
    this.center = position;
  }

  addVelocity(velocity) {
    for (const atom of this.atoms) {
      atom.velocity.add(velocity);
    }
  }
}
