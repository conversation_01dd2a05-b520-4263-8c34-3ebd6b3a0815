import { Vec2 } from "planck";
import Universe from "./universe.js";
import { PhysicalBody } from "./body.js";
import Renderer from "./renderer.js";

await new Promise(resolve => addEventListener("load", resolve));

const [ canvas ] = document.getElementsByTagName("canvas");
const scale = 8;
const renderer = new Renderer(canvas.getContext("2d"), scale);
renderer.resize();
renderer.translate(renderer.canvas.width * 0.5, renderer.canvas.height * 0.5);

const universe = new Universe(renderer);
universe.start();
const bodyA = new PhysicalBody(new Vec2(0, 0), 20, "#f62");
bodyA.isStatic = true;
universe.addBody(bodyA);

let color = 0x001;

const bodyCount = 10;
for(let i = 0; i < bodyCount; i++) {
  const angle = Math.PI / bodyCount * i * 2;
  console.log(angle)
  const radius = 20;
  const body = universe.emplaceBody(
    new Vec2(Math.cos(angle) * radius, Math.sin(angle) * radius),
    Math.floor(Math.random() * 80) / 10 + 1,
    // 4,
    `hsl(${ angle * 180 / Math.PI }deg, 50%, ${ 50 }%)`
  );
}

addEventListener("mousemove", event => bodyA.position.set(event.x / scale - renderer.translatePosition.x, event.y / scale - renderer.translatePosition.y));