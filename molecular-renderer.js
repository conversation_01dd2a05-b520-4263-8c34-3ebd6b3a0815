import { Vec2 } from "planck";

// Vertex shader for atoms
const atomVertexShader = `
  attribute vec2 aPosition;
  attribute vec2 aCenter;
  attribute float aRadius;
  attribute vec4 aColor;

  uniform vec2 uResolution;

  varying vec4 vColor;
  varying vec2 vPosition;

  void main() {
    vec2 position = aPosition * aRadius + aCenter;
    vec2 clipSpace = ((position / uResolution) * 2.0 - 1.0) * vec2(1, -1);
    gl_Position = vec4(clipSpace, 0, 1);
    vColor = aColor;
    vPosition = aPosition;
  }
`;

// Fragment shader for atoms - simple flat circles like original particles
const atomFragmentShader = `
  precision mediump float;
  varying vec4 vColor;
  varying vec2 vPosition;

  void main() {
    float dist = length(vPosition);
    if (dist > 1.0) discard;

    // Simple flat circle like original particles
    gl_FragColor = vColor;
  }
`;

// Vertex shader for bonds
const bondVertexShader = `
  attribute vec2 aPosition;
  uniform vec2 uResolution;

  void main() {
    vec2 clipSpace = ((aPosition / uResolution) * 2.0 - 1.0) * vec2(1, -1);
    gl_Position = vec4(clipSpace, 0, 1);
  }
`;

// Fragment shader for bonds - visible lines
const bondFragmentShader = `
  precision mediump float;

  void main() {
    gl_FragColor = vec4(0.1, 0.1, 0.1, 1.0);
  }
`;

export class MolecularRenderer {
  constructor(gl, resolution) {
    this.gl = gl;
    this.resolution = resolution;
    this.init();
  }

  init() {
    const gl = this.gl;

    // Create atom shader program
    this.atomProgram = this.createProgram(gl, atomVertexShader, atomFragmentShader);

    // Create bond shader program
    this.bondProgram = this.createProgram(gl, bondVertexShader, bondFragmentShader);

    // Get atom attribute locations
    gl.useProgram(this.atomProgram);
    this.atomPositionLocation = gl.getAttribLocation(this.atomProgram, 'aPosition');
    this.atomCenterLocation = gl.getAttribLocation(this.atomProgram, 'aCenter');
    this.atomRadiusLocation = gl.getAttribLocation(this.atomProgram, 'aRadius');
    this.atomColorLocation = gl.getAttribLocation(this.atomProgram, 'aColor');
    this.atomResolutionLocation = gl.getUniformLocation(this.atomProgram, 'uResolution');

    // Get bond attribute locations
    gl.useProgram(this.bondProgram);
    this.bondPositionLocation = gl.getAttribLocation(this.bondProgram, 'aPosition');
    this.bondResolutionLocation = gl.getUniformLocation(this.bondProgram, 'uResolution');

    // Create circle geometry for atoms
    this.createCircleGeometry();

    // Set resolution uniforms
    gl.useProgram(this.atomProgram);
    gl.uniform2f(this.atomResolutionLocation, this.resolution.width, this.resolution.height);

    gl.useProgram(this.bondProgram);
    gl.uniform2f(this.bondResolutionLocation, this.resolution.width, this.resolution.height);
  }

  createProgram(gl, vertexSource, fragmentSource) {
    const vertexShader = this.createShader(gl, gl.VERTEX_SHADER, vertexSource);
    const fragmentShader = this.createShader(gl, gl.FRAGMENT_SHADER, fragmentSource);

    const program = gl.createProgram();
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      console.error('Program link error:', gl.getProgramInfoLog(program));
      return null;
    }

    return program;
  }

  createShader(gl, type, source) {
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);

    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      console.error('Shader compile error:', gl.getShaderInfoLog(shader));
      return null;
    }

    return shader;
  }

  createCircleGeometry() {
    const gl = this.gl;
    const segments = 32;
    const vertices = [0, 0]; // Center point

    for (let i = 0; i <= segments; i++) {
      const angle = (i / segments) * Math.PI * 2;
      vertices.push(Math.cos(angle), Math.sin(angle));
    }

    this.circleBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.circleBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(vertices), gl.STATIC_DRAW);
    this.circleVertexCount = vertices.length / 2;
  }

  render(molecules) {
    const gl = this.gl;

    // Clear the canvas
    gl.clear(gl.COLOR_BUFFER_BIT);

    // Render bonds first (behind atoms)
    this.renderBonds(molecules);

    // Render atoms on top
    this.renderAtoms(molecules);
  }

  renderBonds(molecules) {
    const gl = this.gl;
    gl.useProgram(this.bondProgram);

    const bondVertices = [];

    for (const molecule of molecules) {
      for (const bond of molecule.bonds) {
        const pos1 = bond.atom1.position;
        const pos2 = bond.atom2.position;

        // Create thick, visible bond lines
        const thickness = 8.0;
        const dx = pos2.x - pos1.x;
        const dy = pos2.y - pos1.y;
        const length = Math.sqrt(dx * dx + dy * dy);

        if (length > 0) {
          const perpX = (-dy / length) * thickness;
          const perpY = (dx / length) * thickness;

          // Create quad for thick line
          bondVertices.push(
            pos1.x + perpX, pos1.y + perpY,
            pos1.x - perpX, pos1.y - perpY,
            pos2.x + perpX, pos2.y + perpY,
            pos2.x - perpX, pos2.y - perpY,
            pos2.x + perpX, pos2.y + perpY,
            pos1.x - perpX, pos1.y - perpY
          );
        }
      }
    }

    if (bondVertices.length > 0) {
      const bondBuffer = gl.createBuffer();
      gl.bindBuffer(gl.ARRAY_BUFFER, bondBuffer);
      gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(bondVertices), gl.DYNAMIC_DRAW);

      // Disable any previously enabled vertex attributes
      gl.disableVertexAttribArray(this.atomPositionLocation);
      gl.disableVertexAttribArray(this.atomCenterLocation);
      gl.disableVertexAttribArray(this.atomRadiusLocation);
      gl.disableVertexAttribArray(this.atomColorLocation);

      gl.enableVertexAttribArray(this.bondPositionLocation);
      gl.vertexAttribPointer(this.bondPositionLocation, 2, gl.FLOAT, false, 0, 0);

      gl.drawArrays(gl.TRIANGLES, 0, bondVertices.length / 2);

      gl.disableVertexAttribArray(this.bondPositionLocation);
      gl.deleteBuffer(bondBuffer);
    }
  }

  renderAtoms(molecules) {
    const gl = this.gl;
    gl.useProgram(this.atomProgram);

    // Prepare instance data
    const centers = [];
    const radii = [];
    const colors = [];

    for (const molecule of molecules) {
      for (const atom of molecule.atoms) {
        centers.push(atom.position.x, atom.position.y);
        radii.push(atom.radius * 6); // Much smaller atoms
        colors.push(atom.color.r, atom.color.g, atom.color.b, atom.color.a);
      }
    }

    if (centers.length === 0) return;

    // Bind circle geometry for positions
    gl.bindBuffer(gl.ARRAY_BUFFER, this.circleBuffer);
    gl.enableVertexAttribArray(this.atomPositionLocation);
    gl.vertexAttribPointer(this.atomPositionLocation, 2, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.atomPositionLocation, 0); // Not instanced

    // Create and bind center buffer
    const centerBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, centerBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(centers), gl.DYNAMIC_DRAW);
    gl.enableVertexAttribArray(this.atomCenterLocation);
    gl.vertexAttribPointer(this.atomCenterLocation, 2, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.atomCenterLocation, 1); // Instanced

    // Create and bind radius buffer
    const radiusBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, radiusBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(radii), gl.DYNAMIC_DRAW);
    gl.enableVertexAttribArray(this.atomRadiusLocation);
    gl.vertexAttribPointer(this.atomRadiusLocation, 1, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.atomRadiusLocation, 1); // Instanced

    // Create and bind color buffer
    const colorBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(colors), gl.DYNAMIC_DRAW);
    gl.enableVertexAttribArray(this.atomColorLocation);
    gl.vertexAttribPointer(this.atomColorLocation, 4, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.atomColorLocation, 1); // Instanced

    // Draw instanced atoms
    const numInstances = centers.length / 2;
    gl.drawArraysInstanced(gl.TRIANGLE_FAN, 0, this.circleVertexCount, numInstances);

    // Reset vertex attribute divisors
    gl.vertexAttribDivisor(this.atomCenterLocation, 0);
    gl.vertexAttribDivisor(this.atomRadiusLocation, 0);
    gl.vertexAttribDivisor(this.atomColorLocation, 0);

    // Clean up buffers
    gl.deleteBuffer(centerBuffer);
    gl.deleteBuffer(radiusBuffer);
    gl.deleteBuffer(colorBuffer);
  }
}
