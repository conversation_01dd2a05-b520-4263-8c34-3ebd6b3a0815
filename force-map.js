/** @template T */
export default class ForceMap {

  /** @type {Map<T, Map<T, number>>} */
  #map = new Map;

  /** @param {T[]} keys */
  constructor(keys = []) {
    this.#createForces(keys);
    console.log(this)
  }

  /**
   * @param {T} key
   * @param {number} force
   */
  setInteractor(key, force = 0) {
    this.#set(key, key, 0);
    for(const keyB of this.#map.keys()) {
      this.#set(key, keyB, force);
      this.#set(keyB, key, force);
    }
  }

  /** @param {T[]} keys */
  #createForces(keys) {
    // for(let i = 0; i < keys.length - 1; i++) {
    //   const current = keys[i];
    //   const next = keys[i + 1];
    //   this.#set(current, next, Math.random() + 1);
    //   this.#set(next, current, -Math.random() / 10);
    // }
    for(const keyA of keys) {
      for(const keyB of keys) {
        // Reduced force magnitude for stability
        let force = (Math.random() * 2 - 1) / 6; // Reduced from /3 to /6
        // if(keyA === keyB) {
        //   force -= 0.1;
        // }
        this.#set(keyA, keyB, force);
      }
    }
  }

  /**
   * @param {T} keyA
   * @param {T} keyB
   * @param {number} force
   */
  #set(keyA, keyB, force) {
    if(!this.#map.has(keyA)) {
      this.#map.set(keyA, new Map);
    }
    this.#map.get(keyA).set(keyB, force);
  }

  /**
   * @param {T} keyA
   * @param {T} keyB
   */
  get(keyA, keyB) {
    return this.#map.get(keyA)?.get(keyB);
  }

  /**
   * @template P
   * @template {(new (...args: any[]) => any) & { from: (data: P, mutable?: boolean) => InstanceType<T> }} T
   * @param {T} Type
   * @param {Iterable<[ keyA: P, entry: Iterable<[ keyB: P, value: number ]> ]>} object
   */
  static from(Type, object, valueScale = 1) {
    /** @type {ForceMap<InstanceType<T>>} */
    const instance = new ForceMap;
    for(const [ keyA, entry ] of object) {
      for(const [ keyB, value ] of entry) {
        instance.#set(Type.from(keyA, false), Type.from(keyB, false), value * valueScale);
      }
    }
    return instance;
  }

  toJSON() {
    const data = [];
    for(const entry of this.#map) {
      data.push(/** @type {const} */ ([ entry[0], Array.from(entry[1]) ]));
    }
    return data;
  }

};