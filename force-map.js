/** @template T */
export default class ForceMap {

  /** @type {Map<T, Map<T, number>>} */
  #map = new Map;

  /** @param {T[]} keys */
  constructor(keys = []) {
    this.#createForces(keys);
    console.log(this)
  }

  /**
   * @param {T} key
   * @param {number} force
   */
  setInteractor(key, force = 0) {
    this.#set(key, key, 0);
    for(const keyB of this.#map.keys()) {
      this.#set(key, keyB, force);
      this.#set(keyB, key, force);
    }
  }

  /** @param {T[]} keys */
  #createForces(keys) {
    // Create atomic interactions based on chemistry
    this.#createAtomicForces(keys);
  }

  /** @param {T[]} keys */
  #createAtomicForces(keys) {
    // Define atomic properties (simplified)
    const atomicData = {
      hydrogen: { electronegativity: 2.1, size: 1.0 },
      helium: { electronegativity: 0.0, size: 0.9 },
      lithium: { electronegativity: 1.0, size: 1.8 },
      carbon: { electronegativity: 2.5, size: 1.5 },
      nitrogen: { electronegativity: 3.0, size: 1.4 },
      oxygen: { electronegativity: 3.5, size: 1.3 },
      fluorine: { electronegativity: 4.0, size: 1.2 },
      neon: { electronegativity: 0.0, size: 1.1 },
      sodium: { electronegativity: 0.9, size: 2.2 },
      magnesium: { electronegativity: 1.2, size: 1.9 }
    };

    for(const keyA of keys) {
      for(const keyB of keys) {
        let force = 0;

        // Get atom names from color objects (simplified)
        const atomA = this.#getAtomName(keyA);
        const atomB = this.#getAtomName(keyB);

        if (atomA && atomB && atomicData[atomA] && atomicData[atomB]) {
          const dataA = atomicData[atomA];
          const dataB = atomicData[atomB];

          if (atomA === atomB) {
            // Same atoms: weak repulsion (prevent clustering)
            force = -0.1;
          } else {
            // Different atoms: calculate based on electronegativity difference
            const electronegativityDiff = Math.abs(dataA.electronegativity - dataB.electronegativity);

            if (electronegativityDiff > 1.5) {
              // Ionic bonding (strong attraction)
              force = 0.8;
            } else if (electronegativityDiff > 0.5) {
              // Polar covalent bonding (moderate attraction)
              force = 0.4;
            } else if (electronegativityDiff > 0.1) {
              // Nonpolar covalent bonding (weak attraction)
              force = 0.2;
            } else {
              // Van der Waals forces (very weak)
              force = 0.05;
            }

            // Noble gases (helium, neon) are mostly inert
            if (dataA.electronegativity === 0.0 || dataB.electronegativity === 0.0) {
              force = -0.05; // Weak repulsion
            }
          }
        } else {
          // Fallback for unknown atoms
          force = (Math.random() * 2 - 1) * 0.3;
        }

        this.#set(keyA, keyB, force);
      }
    }
  }

  #getAtomName(colorKey) {
    // Simple mapping - in a real implementation, you'd store this properly
    const colorToAtom = {
      'Color(1, 1, 1, 1)': 'hydrogen',
      'Color(0.85, 1, 1, 1)': 'helium',
      'Color(0.8, 0.5, 1, 1)': 'lithium',
      'Color(0.2, 0.2, 0.2, 1)': 'carbon',
      'Color(0.19, 0.31, 0.97, 1)': 'nitrogen',
      'Color(1, 0.05, 0.05, 1)': 'oxygen',
      'Color(0.56, 0.88, 0.31, 1)': 'fluorine',
      'Color(0.7, 0.89, 0.96, 1)': 'neon',
      'Color(0.67, 0.36, 0.95, 1)': 'sodium',
      'Color(0.54, 1, 0, 1)': 'magnesium'
    };

    return colorToAtom[colorKey.toString()] || null;
  }

  /**
   * @param {T} keyA
   * @param {T} keyB
   * @param {number} force
   */
  #set(keyA, keyB, force) {
    if(!this.#map.has(keyA)) {
      this.#map.set(keyA, new Map);
    }
    this.#map.get(keyA).set(keyB, force);
  }

  /**
   * @param {T} keyA
   * @param {T} keyB
   */
  get(keyA, keyB) {
    return this.#map.get(keyA)?.get(keyB);
  }

  /**
   * @template P
   * @template {(new (...args: any[]) => any) & { from: (data: P, mutable?: boolean) => InstanceType<T> }} T
   * @param {T} Type
   * @param {Iterable<[ keyA: P, entry: Iterable<[ keyB: P, value: number ]> ]>} object
   */
  static from(Type, object, valueScale = 1) {
    /** @type {ForceMap<InstanceType<T>>} */
    const instance = new ForceMap;
    for(const [ keyA, entry ] of object) {
      for(const [ keyB, value ] of entry) {
        instance.#set(Type.from(keyA, false), Type.from(keyB, false), value * valueScale);
      }
    }
    return instance;
  }

  toJSON() {
    const data = [];
    for(const entry of this.#map) {
      data.push(/** @type {const} */ ([ entry[0], Array.from(entry[1]) ]));
    }
    return data;
  }

};