import { Vec2 } from "planck";

export default class Renderer {

  static defaultColor = "#fff";

  #context;
  #zoomLevel;
  #translate = Vec2.zero();

  /** @param {CanvasRenderingContext2D} context */
  constructor(context, scale = 1) {
    this.#context = context;
    this.#context.canvas.width = this.#context.canvas.clientWidth;
    this.#context.canvas.height = this.#context.canvas.clientHeight;
    this.#zoomLevel = scale;
    this.canvasRect = this.#context.canvas.getBoundingClientRect();
  }

  get canvas() {
    return this.#context.canvas;
  }

  get zoomLevel() {
    return this.#zoomLevel;
  }

  get translatePosition() {
    return this.#translate;
  }

  resize() {
    this.#context.canvas.width = this.#context.canvas.parentElement.clientWidth;
    this.#context.canvas.height = this.#context.canvas.parentElement.clientHeight;
    this.canvasRect = this.#context.canvas.getBoundingClientRect();
  }

  /** @param {number} factor */
  zoom(factor) {
    this.#zoomLevel -= factor;
    if(this.#zoomLevel < 0.01) {
      this.#zoomLevel = 0.01;
    }
  }

  /**
   * @param {number} x
   * @param {number} y
   */
  translate(x, y) {
    this.#translate.set(x / this.#zoomLevel, y / this.#zoomLevel);
  }

  /** @param {Vec2} position */
  scaledPosition(position) {
    return position.sub(this.#translate).mul(1 / this.#zoomLevel);
  }

  /** @param {Vec2} position */
  unscaledPosition(position) {
    return Vec2.mulVec2Num(position, this.zoomLevel).sub(this.translatePosition).mul(1 / this.zoomLevel).sub(this.translatePosition)
  }

  clear() {
    this.#context.clearRect(0, 0, this.#context.canvas.width, this.#context.canvas.height);
  }

  /** @param {string} color */
  fill(color) {
    this.#context.fillStyle = color;
    this.#context.fillRect(0, 0, this.#context.canvas.width, this.#context.canvas.height);
  }

  /**
   * @param {Vec2} v1
   * @param {Vec2} v2
   */
  line(v1, v2, lineWidth = 1, color = Renderer.defaultColor) {
    this.#context.beginPath();
    const x1 = (v1.x + this.#translate.x) * this.#zoomLevel;
    const y1 = (v1.y + this.#translate.y) * this.#zoomLevel;
    const x2 = (v2.x + this.#translate.x) * this.#zoomLevel;
    const y2 = (v2.y + this.#translate.y) * this.#zoomLevel;
    this.#context.moveTo(x1, y1);
    this.#context.lineTo(x2, y2);
    this.#context.strokeStyle = color;
    this.#context.lineWidth = lineWidth;
    this.#context.stroke();
    this.#context.closePath();
  }

  /**
   * @param {Vec2} center
   * @param {number} radius
   */
  circle(center, radius, color = Renderer.defaultColor) {
    const x = (center.x + this.#translate.x) * this.#zoomLevel;
    const y = (center.y + this.#translate.y) * this.#zoomLevel;
    this.#context.beginPath();
    this.#context.fillStyle = color;
    this.#context.arc(x, y, radius * this.#zoomLevel, 0, Math.PI * 2);
    this.#context.fill();
    this.#context.closePath();
  }

  /**
   * @param {Vec2} center
   * @param {number} radius
   */
  circleLines(center, radius, lineWidth = 1, color = Renderer.defaultColor) {
    const x = (center.x + this.#translate.x) * this.#zoomLevel;
    const y = (center.y + this.#translate.y) * this.#zoomLevel;
    this.#context.beginPath();
    this.#context.strokeStyle = color;
    this.#context.lineWidth = lineWidth;
    this.#context.arc(x, y, radius * this.#zoomLevel, 0, Math.PI * 2);
    this.#context.stroke();
    this.#context.closePath();
  }

  /**
   * @param {Vec2} center
   * @param {Vec2} radius
   */
  ellipse(center, radius, angle = 0, color = Renderer.defaultColor) {
    const x = (center.x + this.#translate.x) * this.#zoomLevel;
    const y = (center.y + this.#translate.y) * this.#zoomLevel;
    this.#context.fillStyle = color;
    this.#context.ellipse(x, y, radius.x * this.#zoomLevel, radius.y * this.#zoomLevel, angle, 0, Math.PI * 2);
    this.#context.fill();
  }

  /**
   * @param {Vec2} center
   * @param {Vec2} radius
   */
  ellipseLines(center, radius, angle = 0, lineWidth = 1, color = Renderer.defaultColor) {
    const x = (center.x + this.#translate.x) * this.#zoomLevel;
    const y = (center.y + this.#translate.y) * this.#zoomLevel;
    this.#context.beginPath();
    this.#context.strokeStyle = color;
    this.#context.lineWidth = lineWidth;
    this.#context.ellipse(x, y, radius.x * this.#zoomLevel, radius.y * this.#zoomLevel, angle, 0, Math.PI * 2);
    this.#context.stroke();
    this.#context.closePath();
  }

  /**
   * @param {Vec2} v1
   * @param {Vec2} v2
   */
  rectangle(v1, v2, color = Renderer.defaultColor) {
    const x1 = (v1.x + this.#translate.x) * this.#zoomLevel;
    const y1 = (v1.y + this.#translate.y) * this.#zoomLevel;
    const x2 = (v2.x + this.#translate.x) * this.#zoomLevel;
    const y2 = (v2.y + this.#translate.y) * this.#zoomLevel;
    this.#context.fillStyle = color;
    this.#context.fillRect(x1, y1, x2 - x1, y2 - y1);
  }

  /**
   * @param {Vec2} v1
   * @param {Vec2} v2
   */
  rectangleLines(v1, v2, lineWidth = 1, color = Renderer.defaultColor) {
    const x1 = (v1.x + this.#translate.x) * this.#zoomLevel;
    const y1 = (v1.y + this.#translate.y) * this.#zoomLevel;
    const x2 = (v2.x + this.#translate.x) * this.#zoomLevel;
    const y2 = (v2.y + this.#translate.y) * this.#zoomLevel;
    this.#context.beginPath();
    this.#context.strokeStyle = color;
    this.#context.lineWidth = lineWidth;
    this.#context.strokeRect(x1, y1, x2 - x1, y2 - y1);
    this.#context.closePath();
  }

  /**
   * @param {CanvasImageSource} image
   * @param {Vec2} position
   */
  image(image, position = Vec2.zero()) {
    const x = (position.x + this.#translate.x) * this.#zoomLevel;
    const y = (position.y + this.#translate.y) * this.#zoomLevel;
    const width = image.width * this.#zoomLevel;
    const height = image.height * this.#zoomLevel;
    this.#context.drawImage(image, x, y, width, height);
  }

  /**
   * @param {CanvasImageSource} image
   * @param {Rectangle} source
   * @param {Rectangle} destination
   */
  imageSourceDestination(image, source, destination) {
    this.#context.drawImage(
      image,
      source.position.x, source.position.y, source.width, source.height,
      destination.position.x, destination.position.y, destination.width, destination.height
    );
  }

  /**
   * @param {CanvasImageSource} image
   * @param {Rectangle} destination
   */
  imageDestination(image, destination) {
    const x = (destination.position.x + this.#translate.x) * this.#zoomLevel;
    const y = (destination.position.y + this.#translate.y) * this.#zoomLevel;
    const width = destination.width * this.#zoomLevel;
    const height = destination.height * this.#zoomLevel;
    this.#context.drawImage(image, x, y, width, height);
  }

};