import { Vec2 } from "planck";
import GridMap from "./grid-map.js";
import forceMapData from "./data.json" with { type: "json" };
import Teleporter, { TeleporterComponent } from "./teleporter.js";
import Color from "./color.js";
import Particle, { MouseParticle, Vec2Pool } from "./particle.js";
import ForceMap from "./force-map.js";

const sampleName = "sample1";

// Vertex Shader for particles
const vertexShaderSource = `
  attribute vec2 aPosition;
  attribute vec2 aTranslation;
  attribute float aRadius;
  attribute vec4 aColor;

  uniform vec2 uResolution;

  varying vec4 vColor;

  void main() {
      vec2 scaledPosition = aPosition * aRadius;
      vec2 position = scaledPosition + aTranslation;
      vec2 zeroToOne = position / uResolution;
      vec2 zeroToTwo = zeroToOne * 2.0;
      vec2 clipSpace = zeroToTwo - 1.0;
      gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);
      vColor = aColor;
  }

`;

// Vertex Shader for grid lines
const gridVertexShaderSource = `
  attribute vec2 aPosition;
  uniform vec2 uResolution;

  void main() {
      vec2 zeroToOne = aPosition / uResolution;
      vec2 zeroToTwo = zeroToOne * 2.0;
      vec2 clipSpace = zeroToTwo - 1.0;
      gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);
  }
`;

// Fragment Shader for particles
const fragmentShaderSource = `
  precision mediump float;
  varying vec4 vColor;
  void main() {
      gl_FragColor = vColor;
  }
`;

// Fragment Shader for grid lines
const gridFragmentShaderSource = `
  precision mediump float;
  void main() {
      gl_FragColor = vec4(0.2, 0.2, 0.2, 0.3);
  }
`;

const meterPixelRatio = 100;
globalThis.meterPixelRatio = meterPixelRatio;

class ParticleSystem {

  static colors = {
    red: Color.from({ "r": 0.95, "g": 0.26, "b": 0.21 }, false),
    green: Color.from({ "r": 0.30, "g": 0.69, "b": 0.31 }, false),
    blue: Color.from({ "r": 0.13, "g": 0.59, "b": 0.95 }, false),
    yellow: Color.from({ "r": 1.00, "g": 0.92, "b": 0.23 }, false),
    // cyan: new Color(0, 1, 1),
    // magenta: new Color(1, 0, 1),
    // orange: new Color(1, 0.5, 0),
    // purple: new Color(0.5, 0, 0.5),
    pink: Color.from({ "r": 0.81, "g": 0.12, "b": 0.49 }, false),
    // lime: new Color(0.75, 1, 0),
    // teal: new Color(0, 0.5, 0.5),
    // brown: new Color(0.6, 0.3, 0.1),
    // black: new Color(0, 0, 0),
    // white: new Color(1, 1, 1)
  };

  forceMap = sampleName ?
    ForceMap.from(Color, forceMapData[sampleName])
    : new ForceMap(Object.values(ParticleSystem.colors));

  /** @type {GridMap<Particle | TeleporterComponent>} */
  gridMap = new GridMap(16, 16); // Optimal grid size for particle interactions

  // Performance optimization: Pre-allocated typed arrays for GPU buffers
  #maxParticles = 15000; // Allow for growth beyond initial 3000
  #translationArray = new Float32Array(this.#maxParticles * 2); // x, y per particle
  #radiusArray = new Float32Array(this.#maxParticles);
  #colorArray = new Float32Array(this.#maxParticles * 4); // r, g, b, a per particle
  #buffersDirty = true;

  // Grid rendering
  #showGrid = true;

  toggleGrid() {
    this.#showGrid = !this.#showGrid;
    return this.#showGrid;
  }

  /**
   * @param {WebGL2RenderingContext} gl
   * @param {{ width: number; height: number }} resolution
   * @param {Particle[]} particles
   * @param {Teleporter[]} teleporters
   */
  constructor(gl, resolution, particles, teleporters) {
    this.gl = gl;
    this.resolution = resolution;
    this.particles = particles;
    this.teleporters = teleporters;
    this.numVertices = 6;
    for(const particle of this.particles) {
      particle.particleSystem = this;
      particle.gridMapPosition[0] = this.xToGridMapRow(particle.position.x);
      particle.gridMapPosition[1] = this.yToGridMapRow(particle.position.y);
      this.gridMap.add(...particle.gridMapPosition, particle);
    }
    for(const teleporter of this.teleporters) {
      teleporter.particleSystem = this;
      teleporter.inComponent.gridMapPosition[0] = this.xToGridMapRow(teleporter.inComponent.position.x);
      teleporter.inComponent.gridMapPosition[1] = this.yToGridMapRow(teleporter.inComponent.position.y);
      teleporter.outComponent.gridMapPosition[0] = this.xToGridMapRow(teleporter.outComponent.position.x);
      teleporter.outComponent.gridMapPosition[1] = this.yToGridMapRow(teleporter.outComponent.position.y);
      this.gridMap.add(...teleporter.inComponent.gridMapPosition, teleporter.inComponent);
      this.gridMap.add(...teleporter.outComponent.gridMapPosition, teleporter.outComponent);
    }
    this.init();
  }

  /** @param {number} x */
  xToGridMapRow(x) {
    return Math.floor(x / (this.resolution.width / meterPixelRatio) * this.gridMap.rows);
  }

  /** @param {number} y */
  yToGridMapRow(y) {
    return Math.floor(y / (this.resolution.height / meterPixelRatio) * this.gridMap.columns);
  }

  init() {
    const gl = this.gl;

    // Compile shaders for particles
    this.program = this.createProgram(gl, vertexShaderSource, fragmentShaderSource);

    // Compile shaders for grid
    this.gridProgram = this.createProgram(gl, gridVertexShaderSource, gridFragmentShaderSource);

    // Get attribute and uniform locations for particles
    gl.useProgram(this.program);
    this.positionLocation = gl.getAttribLocation(this.program, 'aPosition');
    this.translationLocation = gl.getAttribLocation(this.program, 'aTranslation');
    this.radiusLocation = gl.getAttribLocation(this.program, 'aRadius');
    this.colorLocation = gl.getAttribLocation(this.program, 'aColor');
    this.resolutionLocation = gl.getUniformLocation(this.program, 'uResolution');

    // Get attribute and uniform locations for grid
    gl.useProgram(this.gridProgram);
    this.gridPositionLocation = gl.getAttribLocation(this.gridProgram, 'aPosition');
    this.gridResolutionLocation = gl.getUniformLocation(this.gridProgram, 'uResolution');

    // Create a buffer and put a unit circle in it
    const circleVertices = this.createCircleVertices();

    this.positionBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(circleVertices), gl.STATIC_DRAW);

    // Create buffers for instanced attributes with pre-allocated size
    this.translationBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.translationBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, this.#translationArray, gl.DYNAMIC_DRAW);

    this.radiusBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.radiusBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, this.#radiusArray, gl.DYNAMIC_DRAW);

    this.colorBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.colorBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, this.#colorArray, gl.DYNAMIC_DRAW);

    // Create grid line buffers
    this.createGridBuffers();

    this.updateBuffers();

    // Set resolution uniforms
    gl.useProgram(this.program);
    gl.uniform2f(this.resolutionLocation, this.resolution.width, this.resolution.height);

    gl.useProgram(this.gridProgram);
    gl.uniform2f(this.gridResolutionLocation, this.resolution.width, this.resolution.height);
  }

  createProgram(gl, vertexShaderSource, fragmentShaderSource) {
    const vertexShader = this.createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = this.createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
    const program = gl.createProgram();
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);
    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      console.error('Program failed to link:', gl.getProgramInfoLog(program));
      gl.deleteProgram(program);
      return null;
    }
    return program;
  }

  createShader(gl, type, source) {
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      console.error('Shader compilation failed:', gl.getShaderInfoLog(shader));
      gl.deleteShader(shader);
      return null;
    }
    return shader;
  }

  createCircleVertices() {
    const vertices = [];
    for (let i = 0; i <= this.numVertices; i++) {
      const angle = (i / this.numVertices) * Math.PI * 2;
      vertices.push(Math.cos(angle), Math.sin(angle));
    }
    return vertices;
  }

  createGridBuffers() {
    const gl = this.gl;
    const gridVertices = [];

    const cellWidth = this.resolution.width / this.gridMap.columns;
    const cellHeight = this.resolution.height / this.gridMap.rows;

    // Vertical lines
    for (let i = 0; i <= this.gridMap.columns; i++) {
      const x = i * cellWidth;
      gridVertices.push(x, 0);
      gridVertices.push(x, this.resolution.height);
    }

    // Horizontal lines
    for (let i = 0; i <= this.gridMap.rows; i++) {
      const y = i * cellHeight;
      gridVertices.push(0, y);
      gridVertices.push(this.resolution.width, y);
    }

    this.gridVertexCount = gridVertices.length / 2;

    this.gridBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.gridBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(gridVertices), gl.STATIC_DRAW);
  }

  updateBuffers() {
    if (!this.#buffersDirty) return;

    const gl = this.gl;
    let index = 0;

    // Process teleporters first
    for(const teleporter of this.teleporters) {
      // In component
      this.#translationArray[index * 2] = teleporter.inComponent.position.x * meterPixelRatio;
      this.#translationArray[index * 2 + 1] = teleporter.inComponent.position.y * meterPixelRatio;
      this.#radiusArray[index] = teleporter.inComponent.radius * meterPixelRatio;
      this.#colorArray[index * 4] = teleporter.inComponent.color[0];
      this.#colorArray[index * 4 + 1] = teleporter.inComponent.color[1];
      this.#colorArray[index * 4 + 2] = teleporter.inComponent.color[2];
      this.#colorArray[index * 4 + 3] = teleporter.inComponent.color[3];
      index++;

      // Out component
      this.#translationArray[index * 2] = teleporter.outComponent.position.x * meterPixelRatio;
      this.#translationArray[index * 2 + 1] = teleporter.outComponent.position.y * meterPixelRatio;
      this.#radiusArray[index] = teleporter.outComponent.radius * meterPixelRatio;
      this.#colorArray[index * 4] = teleporter.outComponent.color[0];
      this.#colorArray[index * 4 + 1] = teleporter.outComponent.color[1];
      this.#colorArray[index * 4 + 2] = teleporter.outComponent.color[2];
      this.#colorArray[index * 4 + 3] = teleporter.outComponent.color[3];
      index++;
    }

    // Process particles
    for(const particle of this.particles) {
      this.#translationArray[index * 2] = particle.position.x * meterPixelRatio;
      this.#translationArray[index * 2 + 1] = particle.position.y * meterPixelRatio;
      this.#radiusArray[index] = particle.radius * meterPixelRatio;
      this.#colorArray[index * 4] = particle.color.r;
      this.#colorArray[index * 4 + 1] = particle.color.g;
      this.#colorArray[index * 4 + 2] = particle.color.b;
      this.#colorArray[index * 4 + 3] = particle.color.a;
      index++;
    }

    const numInstances = index;

    // Update GPU buffers with subdata for better performance
    gl.bindBuffer(gl.ARRAY_BUFFER, this.translationBuffer);
    gl.bufferSubData(gl.ARRAY_BUFFER, 0, this.#translationArray.subarray(0, numInstances * 2));

    gl.bindBuffer(gl.ARRAY_BUFFER, this.radiusBuffer);
    gl.bufferSubData(gl.ARRAY_BUFFER, 0, this.#radiusArray.subarray(0, numInstances));

    gl.bindBuffer(gl.ARRAY_BUFFER, this.colorBuffer);
    gl.bufferSubData(gl.ARRAY_BUFFER, 0, this.#colorArray.subarray(0, numInstances * 4));

    this.#buffersDirty = false;
  }

  #counter = 0;
  #processParticlesPerFrame = 1000; // Process more particles per frame for better physics
  #forceCalculationsPerFrame = 0;

  get forceCalculationsPerFrame() {
    return this.#forceCalculationsPerFrame;
  }

  update(deltaTime) {
    // Reset Vec2 pool at the beginning of each frame
    Vec2Pool.reset();

    const rMax = 0.75;
    // for(const particleA of this.particles) {
    //   particleA.acceleration.setZero();
    //   for(const particleB of this.particles) {
    //     if(particleA === particleB) {
    //       continue;
    //     }
    //     const distance = Vec2.distance(particleA.position, particleB.position);
    //     const forceFactor = this.forceMap.get(particleA.color, particleB.color) ?? 0;
    //     const force = this.force(distance / rMax, forceFactor);
    //     const direction = Vec2.sub(particleA.position, particleB.position).mul(1 / distance);
    //     if(force < 0) {
    //       // debugger;
    //     }
    //     particleA.acceleration.add(direction.mul(force * -1 * 2));
    //   }
    //   particleA.acceleration.mul(rMax);
    //   // debugger;
    // }
    const gridWidth = this.resolution.width / meterPixelRatio;
    const gridHeight = this.resolution.height / meterPixelRatio;
    let forceCalculations = 0;
    for(let p = 0; p < this.#processParticlesPerFrame; p++) {
      const index = p + this.#counter * this.#processParticlesPerFrame;
      const particleA = this.particles[index % this.particles.length];
    // for(const particleA of this.gridMap.cell(this.#counter, 16 * 16 / 4)) {
      particleA.acceleration.setZero();
      for(const [row, column, particleB, wrapDirection] of this.gridMap.valuesInRange(...particleA.gridMapPosition, 2)) {
        // console.log(particleA.gridMapPosition, row, column, particleB)
        if(particleA === particleB) {
          continue;
        }
        // Optimize: avoid creating Vec2 objects
        let bx = particleB.position.x;
        let by = particleB.position.y;

        if(wrapDirection.x < 0) {
          bx -= gridWidth;
        } else if(wrapDirection.x > 0) {
          bx += gridWidth;
        }
        if(wrapDirection.y < 0) {
          by -= gridHeight;
        } else if(wrapDirection.y > 0) {
          by += gridHeight;
        }

        const dx = particleA.position.x - bx;
        const dy = particleA.position.y - by;
        const distanceSquared = dx * dx + dy * dy;

        // Early culling: skip particles that are too far away
        const maxInteractionDistance = rMax * 1.5; // Slightly larger than rMax for safety
        if(distanceSquared > maxInteractionDistance * maxInteractionDistance) continue;

        const distance = Math.sqrt(distanceSquared);
        if(distance === 0) continue;

        const invDistance = 1 / distance;
        const dirX = dx * invDistance;
        const dirY = dy * invDistance;

        let force;
        if(particleB instanceof TeleporterComponent) {
          force = particleB.teleporter.handleParticle(particleB, particleA, distance, { x: dirX, y: dirY });
        } else {
          const forceFactor = this.forceMap.get(particleA.actualColor, particleB.actualColor) ?? 0;
          force = this.force(distance / rMax, forceFactor);
        }

        // Clamp force to prevent chaos
        const maxForce = 5.0; // Increased from 2.0 for more energetic interactions
        force = Math.max(-maxForce, Math.min(maxForce, force));

        // Apply force with distance-based scaling to prevent singularities
        const forceScale = Math.min(1.0, distance * 10); // Reduce force at very close distances
        const scaledForce = force * forceScale;

        particleA.acceleration.x += dirX * (-scaledForce);
        particleA.acceleration.y += dirY * (-scaledForce);
        // if(Number.isNaN(particleA.acceleration.x)) {
        //   console.log(distance, -force * distance * 20, direction.mul);
        //   throw "ERR";
        // }
        forceCalculations++;
      }

      // Apply rMax scaling
      particleA.acceleration.x *= rMax;
      particleA.acceleration.y *= rMax;

      // Clamp total acceleration to prevent chaos
      const maxAcceleration = 25.0; // Increased from 10.0 for more responsive movement
      const accelerationMagnitude = Math.sqrt(
        particleA.acceleration.x * particleA.acceleration.x +
        particleA.acceleration.y * particleA.acceleration.y
      );
      if (accelerationMagnitude > maxAcceleration) {
        const scale = maxAcceleration / accelerationMagnitude;
        particleA.acceleration.x *= scale;
        particleA.acceleration.y *= scale;
      }
    }
    this.#counter++;
    // if(this.#counter >= this.gridMap.rows * this.gridMap.columns) {
    //   this.#counter = 0;
    // }
    if(this.#counter * this.#processParticlesPerFrame >= this.particles.length) {
      this.#counter = 0;
    }
    for(const teleporter of this.teleporters) {
      teleporter.update(deltaTime);
    }
    for(const particle of this.particles) {
      particle.update(deltaTime);
      const [ oldRow, oldColumn ] = particle.gridMapPosition;
      particle.gridMapPosition[0] = this.xToGridMapRow(particle.position.x);
      particle.gridMapPosition[1] = this.yToGridMapRow(particle.position.y);
      this.gridMap.move(oldRow, oldColumn, ...particle.gridMapPosition, particle);
    }
    this.#forceCalculationsPerFrame = forceCalculations;
    this.#buffersDirty = true;
    this.updateBuffers();
  }

  #beta = 0.3;

  force(r, a) {
    if(r < this.#beta) {
      return r / this.#beta - 1;
    }
    if(this.#beta < r && r < 1) {
      return a * (1 - Math.abs(2 * r - 1 - this.#beta) / (1 - this.#beta));
    }
    return 0;
  }

  drawGrid() {
    if (!this.#showGrid) return;

    const gl = this.gl;

    gl.useProgram(this.gridProgram);

    // Bind grid vertices
    gl.bindBuffer(gl.ARRAY_BUFFER, this.gridBuffer);
    gl.enableVertexAttribArray(this.gridPositionLocation);
    gl.vertexAttribPointer(this.gridPositionLocation, 2, gl.FLOAT, false, 0, 0);

    // Draw grid lines
    gl.drawArrays(gl.LINES, 0, this.gridVertexCount);
  }

  draw() {
    /** @type {WebGL2RenderingContext} */
    const gl = this.gl;

    // Draw grid first (behind particles)
    this.drawGrid();

    // Draw particles
    gl.useProgram(this.program);

    // Bind circle vertices
    gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
    gl.enableVertexAttribArray(this.positionLocation);
    gl.vertexAttribPointer(this.positionLocation, 2, gl.FLOAT, false, 0, 0);

    // Bind translations
    gl.bindBuffer(gl.ARRAY_BUFFER, this.translationBuffer);
    gl.enableVertexAttribArray(this.translationLocation);
    gl.vertexAttribPointer(this.translationLocation, 2, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.translationLocation, 1);

    // Bind radii
    gl.bindBuffer(gl.ARRAY_BUFFER, this.radiusBuffer);
    gl.enableVertexAttribArray(this.radiusLocation);
    gl.vertexAttribPointer(this.radiusLocation, 1, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.radiusLocation, 1);

    // Bind colors
    gl.bindBuffer(gl.ARRAY_BUFFER, this.colorBuffer);
    gl.enableVertexAttribArray(this.colorLocation);
    gl.vertexAttribPointer(this.colorLocation, 4, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.colorLocation, 1);

    // Draw particles
    const numInstances = this.particles.length + this.teleporters.length * 2;
    gl.drawArraysInstanced(gl.TRIANGLE_FAN, 0, this.numVertices, numInstances);
  }
}

globalThis.colors = sampleName ?
  forceMapData[sampleName].map(entry => Color.from(entry[0], false)) :
  Object.values(ParticleSystem.colors);

function setupWebGL() {
  /** @type {HTMLCanvasElement} */
  const canvas = document.getElementById('demo-canvas');
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;
  const gl = canvas.getContext('webgl2');

  if(gl === null) {
    console.error('WebGL not supported');
    return;
  }

  gl.enable(gl.BLEND);
  gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);

  const resolution = { width: canvas.width, height: canvas.height };
  const radius = 2 / meterPixelRatio; // Back to original size

  const mouseParticle = new MouseParticle({ x: resolution.width / 2, y: resolution.height / 2 });

  const particles = [ mouseParticle ];
  // Start with a good number for testing performance
  const initialParticleCount = 5000;
  for (let i = 1; i < initialParticleCount; i++) {
      const position = {
          x: (Math.random() * resolution.width * 0.9 + resolution.width * 0.05) / meterPixelRatio,
          y: (Math.random() * resolution.height * 0.9 + resolution.height * 0.05) / meterPixelRatio
      };
      const velocity = {
          x: (Math.random() - 0.5) / meterPixelRatio,
          y: (Math.random() - 0.5) / meterPixelRatio
      };
      const color = colors[Math.floor(Math.random() * colors.length)];
      particles.push(new Particle(position, velocity, radius, color));
  }

  const teleporters = [];
  for(let i = 0; i < 2; i++) {
    teleporters.push(
      new Teleporter(
        new TeleporterComponent(
          4,
          { x: resolution.width * Math.random() / meterPixelRatio, y: resolution.height / meterPixelRatio * Math.random() },
          (Math.floor(Math.random() * 40) + 30) / meterPixelRatio,
          new Color(0.1, 0.1, 0.2, 0.25)
        ),
        new TeleporterComponent(
          -4,
          { x: resolution.width * Math.random() / meterPixelRatio, y: resolution.height / meterPixelRatio * Math.random() },
          (Math.floor(Math.random() * 40) + 30) / meterPixelRatio,
          new Color(0.2, 0.1, 0.1, 0.25)
        )
      )
    );
  }

  const particleSystem = new ParticleSystem(gl, resolution, particles, teleporters);
  globalThis.particleSystem = particleSystem;
  particleSystem.forceMap.setInteractor(mouseParticle.actualColor, -1);

  let requestAnimationFrameId;
  let lastFrameTime = Date.now();
  const particleCountDisplay = document.getElementById("particle-count");
  const forceCalcsDisplay = document.getElementById("force-calcs");

  function updateParticleCount() {
    particleCountDisplay.textContent = particleSystem.particles.length;
  }
  updateParticleCount();

  function addParticles(count) {
    for(let i = 0; i < count; i++) {
      const position = {
        x: (Math.random() * resolution.width * 0.9 + resolution.width * 0.05) / meterPixelRatio,
        y: (Math.random() * resolution.height * 0.9 + resolution.height * 0.05) / meterPixelRatio
      };
      const velocity = {
        x: (Math.random() - 0.5) / meterPixelRatio,
        y: (Math.random() - 0.5) / meterPixelRatio
      };
      const color = colors[Math.floor(Math.random() * colors.length)];
      const particle = new Particle(position, velocity, radius, color);
      particle.particleSystem = particleSystem;
      particle.gridMapPosition[0] = particleSystem.xToGridMapRow(particle.position.x);
      particle.gridMapPosition[1] = particleSystem.yToGridMapRow(particle.position.y);
      particleSystem.gridMap.add(...particle.gridMapPosition, particle);
      particleSystem.particles.push(particle);
    }
    updateParticleCount();
  }

  function removeParticles(count) {
    for(let i = 0; i < count && particleSystem.particles.length > 1; i++) {
      const particle = particleSystem.particles.pop();
      if(particle && particle !== mouseParticle) {
        particleSystem.gridMap.delete(...particle.gridMapPosition, particle);
      }
    }
    updateParticleCount();
  }

  function update() {
    const currentTime = Date.now();
    const deltaMs = currentTime - lastFrameTime
    const deltaTime = deltaMs / 1000;
    lastFrameTime = currentTime;

    output.value = Math.floor(1000 / deltaMs);
    forceCalcsDisplay.textContent = particleSystem.forceCalculationsPerFrame;
    particleSystem.update(100 / 1000);
    // particleSystem.update(Math.max(16 / 1000, deltaTime));
    setTimeout(update, 16);
  }

  function render() {
      // console.log(output.value)
      gl.clear(gl.COLOR_BUFFER_BIT);

      particleSystem.draw();

      requestAnimationFrameId = requestAnimationFrame(render);
  }

  function pauseRendering() {
    cancelAnimationFrame(requestAnimationFrameId);
    requestAnimationFrameId = undefined;
  }

  function resumeRendering() {
    lastFrameTime = Date.now();
    render();
  }

  function toggleRendering() {
    if(requestAnimationFrameId === undefined) {
      resumeRendering();
      return true;
    }
    pauseRendering();
    return false;
  }

  window.addEventListener("keydown", function(event) {
    switch(event.code) {
      case "Space": toggleRendering();
      break;
      case "Equal": // + key
      case "NumpadAdd":
        addParticles(500); // Add 500 particles at a time
      break;
      case "Minus":
      case "NumpadSubtract":
        removeParticles(500); // Remove 500 particles at a time
      break;
      case "KeyG":
        const gridVisible = particleSystem.toggleGrid();
        console.log("Grid", gridVisible ? "enabled" : "disabled");
      break;
    }
  });

  gl.clearColor(0, 0, 0, 0);
  update();
  render();
}

setupWebGL();

