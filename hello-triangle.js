import { Vec2 } from "planck";
import { Molecule, MoleculeTemplates, Atom } from "./molecule.js";
import { MolecularRenderer } from "./molecular-renderer.js";
import Color from "./color.js";

// Molecular Simulation System
class MolecularSimulation {
  constructor(canvas) {
    this.canvas = canvas;
    this.gl = canvas.getContext('webgl2');
    this.resolution = { width: canvas.width, height: canvas.height };
    this.molecules = [];
    this.renderer = new MolecularRenderer(this.gl, this.resolution);
    this.init();
  }

  init() {
    const gl = this.gl;
    if (!gl) {
      console.error('WebGL2 not supported');
      return;
    }

    gl.enable(gl.BLEND);
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
    gl.clearColor(0.05, 0.05, 0.1, 1.0);

    this.createInitialMolecules();
  }

  createInitialMolecules() {
    const moleculeTypes = ['water', 'methane', 'ammonia', 'carbonDioxide', 'ethane'];
    const numMolecules = 8; // Fewer molecules to prevent crowding

    for (let i = 0; i < numMolecules; i++) {
      const type = moleculeTypes[Math.floor(Math.random() * moleculeTypes.length)];
      const atoms = MoleculeTemplates[type]();
      const molecule = new Molecule(atoms);

      // Position molecules with better spacing
      let x, y, attempts = 0;
      do {
        x = Math.random() * (this.resolution.width - 200) + 100;
        y = Math.random() * (this.resolution.height - 200) + 100;
        attempts++;
      } while (this.isTooCloseToOtherMolecules(x, y) && attempts < 20);

      molecule.moveTo(new Vec2(x, y));

      // Add smaller random velocity
      const velocity = new Vec2(
        (Math.random() - 0.5) * 20,
        (Math.random() - 0.5) * 20
      );
      molecule.addVelocity(velocity);

      this.molecules.push(molecule);
    }
  }

  isTooCloseToOtherMolecules(x, y) {
    const minDistance = 120;
    for (const molecule of this.molecules) {
      const distance = Vec2.distance(new Vec2(x, y), molecule.center);
      if (distance < minDistance) {
        return true;
      }
    }
    return false;
  }

  addMolecule(type) {
    const atoms = MoleculeTemplates[type]();
    const molecule = new Molecule(atoms);

    // Position with proper spacing
    let x, y, attempts = 0;
    do {
      x = Math.random() * (this.resolution.width - 200) + 100;
      y = Math.random() * (this.resolution.height - 200) + 100;
      attempts++;
    } while (this.isTooCloseToOtherMolecules(x, y) && attempts < 20);

    molecule.moveTo(new Vec2(x, y));

    // Add small random velocity
    const velocity = new Vec2(
      (Math.random() - 0.5) * 15,
      (Math.random() - 0.5) * 15
    );
    molecule.addVelocity(velocity);

    this.molecules.push(molecule);
  }

  update(deltaTime) {
    // Update all molecules
    for (const molecule of this.molecules) {
      molecule.update(deltaTime);
      this.handleBoundaryCollisions(molecule);
    }

    // Handle intermolecular forces (simplified)
    this.applyIntermolecularForces();
  }

  handleBoundaryCollisions(molecule) {
    for (const atom of molecule.atoms) {
      // Bounce off walls
      if (atom.position.x < 20) {
        atom.position.x = 20;
        atom.velocity.x = Math.abs(atom.velocity.x) * 0.8;
      }
      if (atom.position.x > this.resolution.width - 20) {
        atom.position.x = this.resolution.width - 20;
        atom.velocity.x = -Math.abs(atom.velocity.x) * 0.8;
      }
      if (atom.position.y < 20) {
        atom.position.y = 20;
        atom.velocity.y = Math.abs(atom.velocity.y) * 0.8;
      }
      if (atom.position.y > this.resolution.height - 20) {
        atom.position.y = this.resolution.height - 20;
        atom.velocity.y = -Math.abs(atom.velocity.y) * 0.8;
      }
    }
  }

  applyIntermolecularForces() {
    // Prevent molecules from overlapping with repulsion forces
    for (let i = 0; i < this.molecules.length; i++) {
      for (let j = i + 1; j < this.molecules.length; j++) {
        const mol1 = this.molecules[i];
        const mol2 = this.molecules[j];

        const distance = Vec2.distance(mol1.center, mol2.center);
        const minDistance = 80; // Minimum distance between molecule centers

        if (distance < minDistance && distance > 0) {
          // Strong repulsion to prevent overlap
          const overlap = minDistance - distance;
          const force = overlap * 0.02; // Stronger repulsion
          const direction = Vec2.sub(mol1.center, mol2.center);
          direction.normalize();

          const forceVec = Vec2.mul(direction, force);
          mol1.addVelocity(forceVec);
          mol2.addVelocity(Vec2.mul(forceVec, -1));
        }
      }
    }
  }

  render() {
    this.renderer.render(this.molecules);
  }
}

class ParticleSystem {

  // Atomic elements with realistic colors
  static atoms = {
    hydrogen: Color.from({ "r": 1.0, "g": 1.0, "b": 1.0 }, false),    // White
    helium: Color.from({ "r": 0.85, "g": 1.0, "b": 1.0 }, false),     // Light cyan
    lithium: Color.from({ "r": 0.8, "g": 0.5, "b": 1.0 }, false),     // Light purple
    carbon: Color.from({ "r": 0.2, "g": 0.2, "b": 0.2 }, false),      // Dark gray
    nitrogen: Color.from({ "r": 0.19, "g": 0.31, "b": 0.97 }, false), // Blue
    oxygen: Color.from({ "r": 1.0, "g": 0.05, "b": 0.05 }, false),    // Red
    fluorine: Color.from({ "r": 0.56, "g": 0.88, "b": 0.31 }, false), // Light green
    neon: Color.from({ "r": 0.7, "g": 0.89, "b": 0.96 }, false),      // Light blue
    sodium: Color.from({ "r": 0.67, "g": 0.36, "b": 0.95 }, false),   // Purple
    magnesium: Color.from({ "r": 0.54, "g": 1.0, "b": 0.0 }, false),  // Bright green
  };

  forceMap = new ForceMap(Object.values(ParticleSystem.atoms));

  /** @type {GridMap<Particle | TeleporterComponent>} */
  gridMap = new GridMap(16, 16); // Optimal grid size for particle interactions

  // Performance optimization: Pre-allocated typed arrays for GPU buffers
  #maxParticles = 15000; // Allow for growth beyond initial 3000
  #translationArray = new Float32Array(this.#maxParticles * 2); // x, y per particle
  #radiusArray = new Float32Array(this.#maxParticles);
  #colorArray = new Float32Array(this.#maxParticles * 4); // r, g, b, a per particle
  #buffersDirty = true;

  // Grid rendering
  #showGrid = true;

  toggleGrid() {
    this.#showGrid = !this.#showGrid;
    return this.#showGrid;
  }

  /**
   * @param {WebGL2RenderingContext} gl
   * @param {{ width: number; height: number }} resolution
   * @param {Particle[]} particles
   * @param {Teleporter[]} teleporters
   */
  constructor(gl, resolution, particles, teleporters) {
    this.gl = gl;
    this.resolution = resolution;
    this.particles = particles;
    this.teleporters = teleporters;
    this.numVertices = 6;
    for(const particle of this.particles) {
      particle.particleSystem = this;
      particle.gridMapPosition[0] = this.xToGridMapRow(particle.position.x);
      particle.gridMapPosition[1] = this.yToGridMapRow(particle.position.y);
      this.gridMap.add(...particle.gridMapPosition, particle);
    }
    for(const teleporter of this.teleporters) {
      teleporter.particleSystem = this;
      teleporter.inComponent.gridMapPosition[0] = this.xToGridMapRow(teleporter.inComponent.position.x);
      teleporter.inComponent.gridMapPosition[1] = this.yToGridMapRow(teleporter.inComponent.position.y);
      teleporter.outComponent.gridMapPosition[0] = this.xToGridMapRow(teleporter.outComponent.position.x);
      teleporter.outComponent.gridMapPosition[1] = this.yToGridMapRow(teleporter.outComponent.position.y);
      this.gridMap.add(...teleporter.inComponent.gridMapPosition, teleporter.inComponent);
      this.gridMap.add(...teleporter.outComponent.gridMapPosition, teleporter.outComponent);
    }
    this.init();
  }

  /** @param {number} x */
  xToGridMapRow(x) {
    return Math.floor(x / (this.resolution.width / meterPixelRatio) * this.gridMap.rows);
  }

  /** @param {number} y */
  yToGridMapRow(y) {
    return Math.floor(y / (this.resolution.height / meterPixelRatio) * this.gridMap.columns);
  }

  init() {
    const gl = this.gl;

    // Compile shaders for atomic nuclei
    this.nucleusProgram = this.createProgram(gl, nucleusVertexShaderSource, nucleusFragmentShaderSource);

    // Compile shaders for electron clouds
    this.electronProgram = this.createProgram(gl, electronVertexShaderSource, electronFragmentShaderSource);

    // Compile shaders for grid
    this.gridProgram = this.createProgram(gl, gridVertexShaderSource, gridFragmentShaderSource);

    // Get attribute and uniform locations for nucleus
    gl.useProgram(this.nucleusProgram);
    this.nucleusPositionLocation = gl.getAttribLocation(this.nucleusProgram, 'aPosition');
    this.nucleusTranslationLocation = gl.getAttribLocation(this.nucleusProgram, 'aTranslation');
    this.nucleusRadiusLocation = gl.getAttribLocation(this.nucleusProgram, 'aRadius');
    this.nucleusColorLocation = gl.getAttribLocation(this.nucleusProgram, 'aColor');
    this.nucleusResolutionLocation = gl.getUniformLocation(this.nucleusProgram, 'uResolution');

    // Get attribute and uniform locations for electrons
    gl.useProgram(this.electronProgram);
    this.electronPositionLocation = gl.getAttribLocation(this.electronProgram, 'aPosition');
    this.electronTranslationLocation = gl.getAttribLocation(this.electronProgram, 'aTranslation');
    this.electronRadiusLocation = gl.getAttribLocation(this.electronProgram, 'aRadius');
    this.electronColorLocation = gl.getAttribLocation(this.electronProgram, 'aColor');
    this.electronResolutionLocation = gl.getUniformLocation(this.electronProgram, 'uResolution');

    // Get attribute and uniform locations for grid
    gl.useProgram(this.gridProgram);
    this.gridPositionLocation = gl.getAttribLocation(this.gridProgram, 'aPosition');
    this.gridResolutionLocation = gl.getUniformLocation(this.gridProgram, 'uResolution');

    // Create a buffer and put a unit circle in it
    const circleVertices = this.createCircleVertices();

    this.positionBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(circleVertices), gl.STATIC_DRAW);

    // Create buffers for instanced attributes with pre-allocated size
    this.translationBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.translationBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, this.#translationArray, gl.DYNAMIC_DRAW);

    this.radiusBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.radiusBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, this.#radiusArray, gl.DYNAMIC_DRAW);

    this.colorBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.colorBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, this.#colorArray, gl.DYNAMIC_DRAW);

    // Create grid line buffers
    this.createGridBuffers();

    this.updateBuffers();

    // Set resolution uniforms
    gl.useProgram(this.nucleusProgram);
    gl.uniform2f(this.nucleusResolutionLocation, this.resolution.width, this.resolution.height);

    gl.useProgram(this.electronProgram);
    gl.uniform2f(this.electronResolutionLocation, this.resolution.width, this.resolution.height);

    gl.useProgram(this.gridProgram);
    gl.uniform2f(this.gridResolutionLocation, this.resolution.width, this.resolution.height);
  }

  createProgram(gl, vertexShaderSource, fragmentShaderSource) {
    const vertexShader = this.createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = this.createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
    const program = gl.createProgram();
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);
    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      console.error('Program failed to link:', gl.getProgramInfoLog(program));
      gl.deleteProgram(program);
      return null;
    }
    return program;
  }

  createShader(gl, type, source) {
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      console.error('Shader compilation failed:', gl.getShaderInfoLog(shader));
      gl.deleteShader(shader);
      return null;
    }
    return shader;
  }

  createCircleVertices() {
    const vertices = [];
    for (let i = 0; i <= this.numVertices; i++) {
      const angle = (i / this.numVertices) * Math.PI * 2;
      vertices.push(Math.cos(angle), Math.sin(angle));
    }
    return vertices;
  }

  createGridBuffers() {
    const gl = this.gl;
    const gridVertices = [];

    const cellWidth = this.resolution.width / this.gridMap.columns;
    const cellHeight = this.resolution.height / this.gridMap.rows;

    // Vertical lines
    for (let i = 0; i <= this.gridMap.columns; i++) {
      const x = i * cellWidth;
      gridVertices.push(x, 0);
      gridVertices.push(x, this.resolution.height);
    }

    // Horizontal lines
    for (let i = 0; i <= this.gridMap.rows; i++) {
      const y = i * cellHeight;
      gridVertices.push(0, y);
      gridVertices.push(this.resolution.width, y);
    }

    this.gridVertexCount = gridVertices.length / 2;

    this.gridBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.gridBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(gridVertices), gl.STATIC_DRAW);
  }

  updateBuffers() {
    if (!this.#buffersDirty) return;

    const gl = this.gl;
    let index = 0;

    // Process teleporters first
    for(const teleporter of this.teleporters) {
      // In component
      this.#translationArray[index * 2] = teleporter.inComponent.position.x * meterPixelRatio;
      this.#translationArray[index * 2 + 1] = teleporter.inComponent.position.y * meterPixelRatio;
      this.#radiusArray[index] = teleporter.inComponent.radius * meterPixelRatio;
      this.#colorArray[index * 4] = teleporter.inComponent.color[0];
      this.#colorArray[index * 4 + 1] = teleporter.inComponent.color[1];
      this.#colorArray[index * 4 + 2] = teleporter.inComponent.color[2];
      this.#colorArray[index * 4 + 3] = teleporter.inComponent.color[3];
      index++;

      // Out component
      this.#translationArray[index * 2] = teleporter.outComponent.position.x * meterPixelRatio;
      this.#translationArray[index * 2 + 1] = teleporter.outComponent.position.y * meterPixelRatio;
      this.#radiusArray[index] = teleporter.outComponent.radius * meterPixelRatio;
      this.#colorArray[index * 4] = teleporter.outComponent.color[0];
      this.#colorArray[index * 4 + 1] = teleporter.outComponent.color[1];
      this.#colorArray[index * 4 + 2] = teleporter.outComponent.color[2];
      this.#colorArray[index * 4 + 3] = teleporter.outComponent.color[3];
      index++;
    }

    // Process particles
    for(const particle of this.particles) {
      this.#translationArray[index * 2] = particle.position.x * meterPixelRatio;
      this.#translationArray[index * 2 + 1] = particle.position.y * meterPixelRatio;
      this.#radiusArray[index] = particle.radius * meterPixelRatio;
      this.#colorArray[index * 4] = particle.color.r;
      this.#colorArray[index * 4 + 1] = particle.color.g;
      this.#colorArray[index * 4 + 2] = particle.color.b;
      this.#colorArray[index * 4 + 3] = particle.color.a;
      index++;
    }

    const numInstances = index;

    // Update GPU buffers with subdata for better performance
    gl.bindBuffer(gl.ARRAY_BUFFER, this.translationBuffer);
    gl.bufferSubData(gl.ARRAY_BUFFER, 0, this.#translationArray.subarray(0, numInstances * 2));

    gl.bindBuffer(gl.ARRAY_BUFFER, this.radiusBuffer);
    gl.bufferSubData(gl.ARRAY_BUFFER, 0, this.#radiusArray.subarray(0, numInstances));

    gl.bindBuffer(gl.ARRAY_BUFFER, this.colorBuffer);
    gl.bufferSubData(gl.ARRAY_BUFFER, 0, this.#colorArray.subarray(0, numInstances * 4));

    this.#buffersDirty = false;
  }

  #counter = 0;
  #processParticlesPerFrame = 1000; // Process more particles per frame for better physics
  #forceCalculationsPerFrame = 0;

  get forceCalculationsPerFrame() {
    return this.#forceCalculationsPerFrame;
  }

  update(deltaTime) {
    // Reset Vec2 pool at the beginning of each frame
    Vec2Pool.reset();

    const rMax = 0.4; // Optimal range for particle life systems
    // for(const particleA of this.particles) {
    //   particleA.acceleration.setZero();
    //   for(const particleB of this.particles) {
    //     if(particleA === particleB) {
    //       continue;
    //     }
    //     const distance = Vec2.distance(particleA.position, particleB.position);
    //     const forceFactor = this.forceMap.get(particleA.color, particleB.color) ?? 0;
    //     const force = this.force(distance / rMax, forceFactor);
    //     const direction = Vec2.sub(particleA.position, particleB.position).mul(1 / distance);
    //     if(force < 0) {
    //       // debugger;
    //     }
    //     particleA.acceleration.add(direction.mul(force * -1 * 2));
    //   }
    //   particleA.acceleration.mul(rMax);
    //   // debugger;
    // }
    const gridWidth = this.resolution.width / meterPixelRatio;
    const gridHeight = this.resolution.height / meterPixelRatio;
    let forceCalculations = 0;
    for(let p = 0; p < this.#processParticlesPerFrame; p++) {
      const index = p + this.#counter * this.#processParticlesPerFrame;
      const particleA = this.particles[index % this.particles.length];
    // for(const particleA of this.gridMap.cell(this.#counter, 16 * 16 / 4)) {
      particleA.acceleration.setZero();
      for(const [row, column, particleB, wrapDirection] of this.gridMap.valuesInRange(...particleA.gridMapPosition, 2)) {
        // console.log(particleA.gridMapPosition, row, column, particleB)
        if(particleA === particleB) {
          continue;
        }
        // Optimize: avoid creating Vec2 objects
        let bx = particleB.position.x;
        let by = particleB.position.y;

        if(wrapDirection.x < 0) {
          bx -= gridWidth;
        } else if(wrapDirection.x > 0) {
          bx += gridWidth;
        }
        if(wrapDirection.y < 0) {
          by -= gridHeight;
        } else if(wrapDirection.y > 0) {
          by += gridHeight;
        }

        const dx = particleA.position.x - bx;
        const dy = particleA.position.y - by;
        const distanceSquared = dx * dx + dy * dy;

        // No distance culling - let the force function handle range naturally

        const distance = Math.sqrt(distanceSquared);
        if(distance === 0) continue;

        const invDistance = 1 / distance;
        const dirX = dx * invDistance;
        const dirY = dy * invDistance;

        let force;
        if(particleB instanceof TeleporterComponent) {
          force = particleB.teleporter.handleParticle(particleB, particleA, distance, { x: dirX, y: dirY });
        } else {
          const forceFactor = this.forceMap.get(particleA.actualColor, particleB.actualColor) ?? 0;
          force = this.force(distance / rMax, forceFactor);
        }

        // Clamp force to prevent chaos
        const maxForce = 20.0; // High limit for particle life dynamics
        force = Math.max(-maxForce, Math.min(maxForce, force));

        // Apply force directly for better clustering
        particleA.acceleration.x += dirX * (-force);
        particleA.acceleration.y += dirY * (-force);
        // if(Number.isNaN(particleA.acceleration.x)) {
        //   console.log(distance, -force * distance * 20, direction.mul);
        //   throw "ERR";
        // }
        forceCalculations++;
      }

      // Apply rMax scaling
      particleA.acceleration.x *= rMax;
      particleA.acceleration.y *= rMax;

      // Clamp total acceleration to prevent chaos
      const maxAcceleration = 25.0; // Increased from 10.0 for more responsive movement
      const accelerationMagnitude = Math.sqrt(
        particleA.acceleration.x * particleA.acceleration.x +
        particleA.acceleration.y * particleA.acceleration.y
      );
      if (accelerationMagnitude > maxAcceleration) {
        const scale = maxAcceleration / accelerationMagnitude;
        particleA.acceleration.x *= scale;
        particleA.acceleration.y *= scale;
      }
    }
    this.#counter++;
    // if(this.#counter >= this.gridMap.rows * this.gridMap.columns) {
    //   this.#counter = 0;
    // }
    if(this.#counter * this.#processParticlesPerFrame >= this.particles.length) {
      this.#counter = 0;
    }
    for(const teleporter of this.teleporters) {
      teleporter.update(deltaTime);
    }
    for(const particle of this.particles) {
      particle.update(deltaTime);
      const [ oldRow, oldColumn ] = particle.gridMapPosition;
      particle.gridMapPosition[0] = this.xToGridMapRow(particle.position.x);
      particle.gridMapPosition[1] = this.yToGridMapRow(particle.position.y);
      this.gridMap.move(oldRow, oldColumn, ...particle.gridMapPosition, particle);
    }
    this.#forceCalculationsPerFrame = forceCalculations;
    this.#buffersDirty = true;
    this.updateBuffers();
  }

  #beta = 0.3; // Optimal beta for particle life dynamics

  force(r, a) {
    if(r < this.#beta) {
      return r / this.#beta - 1;
    }
    if(this.#beta < r && r < 1) {
      return a * (1 - Math.abs(2 * r - 1 - this.#beta) / (1 - this.#beta));
    }
    return 0;
  }

  drawGrid() {
    if (!this.#showGrid) return;

    const gl = this.gl;

    gl.useProgram(this.gridProgram);

    // Bind grid vertices
    gl.bindBuffer(gl.ARRAY_BUFFER, this.gridBuffer);
    gl.enableVertexAttribArray(this.gridPositionLocation);
    gl.vertexAttribPointer(this.gridPositionLocation, 2, gl.FLOAT, false, 0, 0);

    // Draw grid lines
    gl.drawArrays(gl.LINES, 0, this.gridVertexCount);
  }

  draw() {
    /** @type {WebGL2RenderingContext} */
    const gl = this.gl;

    // Draw grid first (behind atoms)
    this.drawGrid();

    // Draw electron clouds first (behind nuclei)
    this.drawElectronClouds();

    // Draw atomic nuclei on top
    this.drawNuclei();
  }

  drawElectronClouds() {
    const gl = this.gl;

    gl.useProgram(this.electronProgram);

    // Bind circle vertices
    gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
    gl.enableVertexAttribArray(this.electronPositionLocation);
    gl.vertexAttribPointer(this.electronPositionLocation, 2, gl.FLOAT, false, 0, 0);

    // Bind translations
    gl.bindBuffer(gl.ARRAY_BUFFER, this.translationBuffer);
    gl.enableVertexAttribArray(this.electronTranslationLocation);
    gl.vertexAttribPointer(this.electronTranslationLocation, 2, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.electronTranslationLocation, 1);

    // Bind radii
    gl.bindBuffer(gl.ARRAY_BUFFER, this.radiusBuffer);
    gl.enableVertexAttribArray(this.electronRadiusLocation);
    gl.vertexAttribPointer(this.electronRadiusLocation, 1, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.electronRadiusLocation, 1);

    // Bind colors (dimmed for electron clouds)
    gl.bindBuffer(gl.ARRAY_BUFFER, this.colorBuffer);
    gl.enableVertexAttribArray(this.electronColorLocation);
    gl.vertexAttribPointer(this.electronColorLocation, 4, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.electronColorLocation, 1);

    // Draw electron clouds
    const numInstances = this.particles.length + this.teleporters.length * 2;
    gl.drawArraysInstanced(gl.TRIANGLE_FAN, 0, this.numVertices, numInstances);
  }

  drawNuclei() {
    const gl = this.gl;

    gl.useProgram(this.nucleusProgram);

    // Bind circle vertices
    gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
    gl.enableVertexAttribArray(this.nucleusPositionLocation);
    gl.vertexAttribPointer(this.nucleusPositionLocation, 2, gl.FLOAT, false, 0, 0);

    // Bind translations
    gl.bindBuffer(gl.ARRAY_BUFFER, this.translationBuffer);
    gl.enableVertexAttribArray(this.nucleusTranslationLocation);
    gl.vertexAttribPointer(this.nucleusTranslationLocation, 2, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.nucleusTranslationLocation, 1);

    // Bind radii
    gl.bindBuffer(gl.ARRAY_BUFFER, this.radiusBuffer);
    gl.enableVertexAttribArray(this.nucleusRadiusLocation);
    gl.vertexAttribPointer(this.nucleusRadiusLocation, 1, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.nucleusRadiusLocation, 1);

    // Bind colors
    gl.bindBuffer(gl.ARRAY_BUFFER, this.colorBuffer);
    gl.enableVertexAttribArray(this.nucleusColorLocation);
    gl.vertexAttribPointer(this.nucleusColorLocation, 4, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.nucleusColorLocation, 1);

    // Draw nuclei
    const numInstances = this.particles.length + this.teleporters.length * 2;
    gl.drawArraysInstanced(gl.TRIANGLE_FAN, 0, this.numVertices, numInstances);
  }
}

globalThis.colors = Object.values(ParticleSystem.atoms);

function setupMolecularSimulation() {
  /** @type {HTMLCanvasElement} */
  const canvas = document.getElementById('demo-canvas');
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;

  // Create molecular simulation
  const simulation = new MolecularSimulation(canvas);
  globalThis.simulation = simulation;

  // UI elements
  let requestAnimationFrameId;
  let lastFrameTime = Date.now();
  const moleculeCountDisplay = document.getElementById("particle-count");

  function updateMoleculeCount() {
    moleculeCountDisplay.textContent = simulation.molecules.length;
  }
  updateMoleculeCount();

  function addMolecule() {
    const moleculeTypes = ['water', 'methane', 'ammonia', 'carbonDioxide', 'ethane'];
    const type = moleculeTypes[Math.floor(Math.random() * moleculeTypes.length)];
    simulation.addMolecule(type);
    updateMoleculeCount();
  }

  function removeMolecule() {
    if (simulation.molecules.length > 1) {
      simulation.molecules.pop();
      updateMoleculeCount();
    }
  }

  function update() {
    const currentTime = Date.now();
    const deltaMs = currentTime - lastFrameTime;
    const deltaTime = deltaMs / 1000;
    lastFrameTime = currentTime;

    output.value = Math.floor(1000 / deltaMs);
    simulation.update(deltaTime);
    setTimeout(update, 16);
  }

  function render() {
    simulation.render();
    requestAnimationFrameId = requestAnimationFrame(render);
  }

  function pauseRendering() {
    cancelAnimationFrame(requestAnimationFrameId);
    requestAnimationFrameId = undefined;
  }

  function resumeRendering() {
    lastFrameTime = Date.now();
    render();
  }

  function toggleRendering() {
    if(requestAnimationFrameId === undefined) {
      resumeRendering();
      return true;
    }
    pauseRendering();
    return false;
  }

  window.addEventListener("keydown", function(event) {
    switch(event.code) {
      case "Space":
        toggleRendering();
        break;
      case "Equal": // + key
      case "NumpadAdd":
        addMolecule();
        break;
      case "Minus":
      case "NumpadSubtract":
        removeMolecule();
        break;
    }
  });

  update();
  render();
}

setupMolecularSimulation();

